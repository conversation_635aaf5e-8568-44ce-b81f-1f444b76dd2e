{"name": "legalscout", "private": true, "version": "0.1.0", "type": "module", "scripts": {"start": "vite", "dev": "cross-env NODE_ENV=development vite --port 5174 --host", "dev:api": "cross-env NODE_ENV=development node simple-dev-server.js", "dev:api:full": "cross-env NODE_ENV=development node dev-server.js", "dev:full": "concurrently \"npm run dev:api\" \"npm run dev\"", "dev:quiet": "cross-env NODE_ENV=development VITE_LOGGING=error vite --port 5174 --host", "test:call-sync": "node scripts/run-call-sync-tests.js", "check:vapi-config": "node scripts/check-vapi-config.js", "diagnose:call-sync": "npm run check:vapi-config && npm run test:call-sync", "dev:profile": "cross-env REACT_PROFILER=1 vite --port 5174 --host", "dev:debug": "cross-env REACT_DEBUG_TOOLS=1 vite --port 5174 --host", "build": "vite build && node scripts/copy-fix-scripts.js", "build:profile": "cross-env REACT_PROFILER=1 vite build && node scripts/copy-fix-scripts.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "clean": "rimraf dist node_modules/.vite", "reset": "npm run clean && npm install", "test": "vitest run", "test:vapi-mcp": "node scripts/test-vapi-mcp-connection.js", "test:vapi-key": "node scripts/test-vapi-api-key.js", "vercel-build": "cross-env NODE_ENV=production VITE_DISABLE_FRAMER_MOTION=true vite build && node scripts/copy-fix-scripts.js"}, "dependencies": {"@agentdeskai/browser-tools-mcp": "^1.2.1", "@auth0/auth0-react": "^2.2.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@modelcontextprotocol/sdk": "^1.12.1", "@mui/material": "^7.1.0", "@react-three/drei": "^10.0.4", "@react-three/fiber": "^9.1.0", "@supabase/supabase-js": "^2.49.1", "@vapi-ai/mcp-server": "^0.0.6", "@vapi-ai/web": "^2.3.1", "@vercel/mcp-adapter": "^0.6.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "formidable": "^3.5.4", "framer-motion": "^10.16.4", "isomorphic-fetch": "^3.0.0", "jose": "^6.0.11", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "papaparse": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.3.0", "react-toastify": "^11.0.5", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "three": "^0.174.0", "three-globe": "^2.42.1", "typewriter-effect": "^2.21.0", "uuid": "^11.1.0", "vm2": "^3.9.19", "ws": "^8.18.2", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.25.9", "@browsermcp/mcp": "^0.1.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/react-hooks": "^8.0.1", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eventsource": "^4.0.0", "node-fetch": "^3.3.2", "postcss": "^8.5.3", "postcss-preset-env": "^10.1.5", "rimraf": "^5.0.10", "supertest": "^7.1.1", "tailwindcss": "^3.3.3", "vite": "^4.4.5", "vitest": "^3.0.8"}, "description": "LegalScout is a voice-guided legal consultation platform that connects users with attorneys based on their legal needs. The platform uses Vapi.ai for voice interactions and provides interactive map visualizations for attorney recommendations.", "main": "checkSupabaseTables.js", "directories": {"doc": "docs", "test": "tests"}, "keywords": [], "author": "", "license": "ISC"}