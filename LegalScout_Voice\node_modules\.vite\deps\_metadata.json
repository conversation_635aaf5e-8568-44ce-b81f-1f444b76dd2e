{"hash": "f3974b77", "browserHash": "ef5687ec", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "b1363721", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "0bf952c3", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "ad2099e2", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "276af782", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "acfcabdd", "needsInterop": true}, "@modelcontextprotocol/sdk/client/index.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/index.js", "file": "@modelcontextprotocol_sdk_client_index__js.js", "fileHash": "c0e12f71", "needsInterop": false}, "@modelcontextprotocol/sdk/client/sse.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/sse.js", "file": "@modelcontextprotocol_sdk_client_sse__js.js", "fileHash": "0867a843", "needsInterop": false}, "@modelcontextprotocol/sdk/client/streamableHttp.js": {"src": "../../@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js", "file": "@modelcontextprotocol_sdk_client_streamableHttp__js.js", "fileHash": "352fb2da", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "b5a970af", "needsInterop": false}, "@vapi-ai/web": {"src": "../../@vapi-ai/web/dist/vapi.js", "file": "@vapi-ai_web.js", "fileHash": "8efd88d7", "needsInterop": true}, "jose": {"src": "../../jose/dist/webapi/index.js", "file": "jose.js", "fileHash": "ca817f54", "needsInterop": false}, "leaflet": {"src": "../../leaflet/dist/leaflet-src.js", "file": "leaflet.js", "fileHash": "9b51c7da", "needsInterop": true}, "papaparse": {"src": "../../papaparse/papaparse.min.js", "file": "papaparse.js", "fileHash": "3b53e9cf", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5c7e6635", "needsInterop": true}, "react-dom/server": {"src": "../../react-dom/server.browser.js", "file": "react-dom_server.js", "fileHash": "2f41d333", "needsInterop": true}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "9b47c688", "needsInterop": false}, "react-icons/md": {"src": "../../react-icons/md/index.mjs", "file": "react-icons_md.js", "fileHash": "22ec63a7", "needsInterop": false}, "react-markdown": {"src": "../../react-markdown/index.js", "file": "react-markdown.js", "fileHash": "3bb6adc9", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "7016bf05", "needsInterop": false}, "rehype-raw": {"src": "../../rehype-raw/index.js", "file": "rehype-raw.js", "fileHash": "aa4b40e4", "needsInterop": false}, "rehype-sanitize": {"src": "../../rehype-sanitize/index.js", "file": "rehype-sanitize.js", "fileHash": "c88a6456", "needsInterop": false}, "remark-gfm": {"src": "../../remark-gfm/index.js", "file": "remark-gfm.js", "fileHash": "40dc7c5d", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "9338386d", "needsInterop": false}, "xlsx": {"src": "../../xlsx/xlsx.mjs", "file": "xlsx.js", "fileHash": "28faace5", "needsInterop": false}}, "chunks": {"browser-2H53BKCB": {"file": "browser-2H53BKCB.js"}, "browser-L3WHH2Q2": {"file": "browser-L3WHH2Q2.js"}, "chunk-CUTKQHYX": {"file": "chunk-CUTKQHYX.js"}, "chunk-FYR2ONTC": {"file": "chunk-FYR2ONTC.js"}, "chunk-ZKFSI6YS": {"file": "chunk-ZKFSI6YS.js"}, "chunk-WLB7R6ZN": {"file": "chunk-WLB7R6ZN.js"}, "chunk-EFHT7PV7": {"file": "chunk-EFHT7PV7.js"}, "chunk-A5ED6EHL": {"file": "chunk-A5ED6EHL.js"}, "chunk-3TH3G7JX": {"file": "chunk-3TH3G7JX.js"}, "chunk-Q72EVS5P": {"file": "chunk-Q72EVS5P.js"}, "chunk-73YGM6QR": {"file": "chunk-73YGM6QR.js"}, "chunk-2N3A5BUM": {"file": "chunk-2N3A5BUM.js"}, "chunk-J55MB4ZD": {"file": "chunk-J55MB4ZD.js"}, "chunk-SJSTY3YX": {"file": "chunk-SJSTY3YX.js"}, "chunk-C3M7BXFS": {"file": "chunk-C3M7BXFS.js"}}}