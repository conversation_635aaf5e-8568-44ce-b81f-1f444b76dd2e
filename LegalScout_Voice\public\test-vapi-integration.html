<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .status.warning { background: #fff3cd; color: #856404; }
        button {
            background: #4B74AA;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover {
            background: #3a5d8a;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .call-controls {
            margin: 20px 0;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
        }
        .log-entry.error { color: #dc3545; }
        .log-entry.success { color: #28a745; }
        .log-entry.info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Vapi Integration Test</h1>
        <p>This page tests the Vapi integration for LegalScout Voice</p>

        <!-- Configuration Display -->
        <div class="test-section">
            <h3>📋 Configuration</h3>
            <div id="config-display">Loading configuration...</div>
        </div>

        <!-- SDK Loading Test -->
        <div class="test-section">
            <h3>📦 SDK Loading Test</h3>
            <button onclick="testSDKLoading()">Test SDK Loading</button>
            <div id="sdk-status" class="status info">Click button to test SDK loading</div>
        </div>

        <!-- API Connection Test -->
        <div class="test-section">
            <h3>🔗 API Connection Test</h3>
            <button onclick="testAPIConnection()">Test API Connection</button>
            <div id="api-status" class="status info">Click button to test API connection</div>
        </div>

        <!-- Vapi Call Test -->
        <div class="test-section">
            <h3>📞 Vapi Call Test</h3>
            <div class="call-controls">
                <button id="start-call-btn" onclick="startVapiCall()">Start Call</button>
                <button id="stop-call-btn" onclick="stopVapiCall()" disabled>Stop Call</button>
            </div>
            <div id="call-status" class="status info">Ready to start call</div>
            <div id="call-messages"></div>
        </div>

        <!-- Debug Log -->
        <div class="test-section">
            <h3>🐛 Debug Log</h3>
            <button onclick="clearLog()">Clear Log</button>
            <div id="debug-log" class="log"></div>
        </div>
    </div>

    <!-- Load Vapi SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/index.js"></script>
    
    <script>
        // Configuration
        const CONFIG = {
            apiKey: '6734febc-fc65-4669-93b0-929b31ff6564',
            assistantId: 'f9b97d13-f9c4-40af-a660-62ba5925ff2a',
            baseUrl: 'https://api.vapi.ai'
        };

        let vapi = null;
        let isCallActive = false;

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            const debugLog = document.getElementById('debug-log');
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
            
            console.log(`[VapiTest] ${message}`);
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // Display configuration
        function displayConfig() {
            const configDisplay = document.getElementById('config-display');
            configDisplay.innerHTML = `
                <strong>API Key:</strong> ${CONFIG.apiKey.substring(0, 8)}...<br>
                <strong>Assistant ID:</strong> ${CONFIG.assistantId}<br>
                <strong>Base URL:</strong> ${CONFIG.baseUrl}
            `;
        }

        // Test SDK Loading
        async function testSDKLoading() {
            log('Testing SDK loading...', 'info');
            updateStatus('sdk-status', 'Testing SDK loading...', 'info');

            try {
                // Check if Vapi is available
                if (typeof window.Vapi === 'undefined') {
                    throw new Error('Vapi SDK not loaded');
                }

                log('Vapi SDK found in window object', 'success');
                
                // Try to create a Vapi instance
                vapi = new window.Vapi(CONFIG.apiKey);
                log('Vapi instance created successfully', 'success');
                
                updateStatus('sdk-status', 'SDK loaded successfully', 'success');
                return true;
            } catch (error) {
                log(`SDK loading failed: ${error.message}`, 'error');
                updateStatus('sdk-status', `SDK loading failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Test API Connection
        async function testAPIConnection() {
            log('Testing API connection...', 'info');
            updateStatus('api-status', 'Testing API connection...', 'info');

            try {
                // Test with a simple fetch to the API
                const response = await fetch(`${CONFIG.baseUrl}/assistant/${CONFIG.assistantId}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${CONFIG.apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`API connection successful. Assistant name: ${data.name || 'Unknown'}`, 'success');
                    updateStatus('api-status', 'API connection successful', 'success');
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                log(`API connection failed: ${error.message}`, 'error');
                updateStatus('api-status', `API connection failed: ${error.message}`, 'error');
                return false;
            }
        }

        // Start Vapi Call
        async function startVapiCall() {
            log('Starting Vapi call...', 'info');
            updateStatus('call-status', 'Starting call...', 'info');

            try {
                // Ensure SDK is loaded
                if (!vapi) {
                    const sdkLoaded = await testSDKLoading();
                    if (!sdkLoaded) {
                        throw new Error('Failed to load SDK');
                    }
                }

                // Set up event listeners
                vapi.on('call-start', () => {
                    log('Call started successfully', 'success');
                    updateStatus('call-status', 'Call active', 'success');
                    isCallActive = true;
                    document.getElementById('start-call-btn').disabled = true;
                    document.getElementById('stop-call-btn').disabled = false;
                });

                vapi.on('call-end', () => {
                    log('Call ended', 'info');
                    updateStatus('call-status', 'Call ended', 'info');
                    isCallActive = false;
                    document.getElementById('start-call-btn').disabled = false;
                    document.getElementById('stop-call-btn').disabled = true;
                });

                vapi.on('message', (message) => {
                    log(`Message received: ${JSON.stringify(message)}`, 'info');
                });

                vapi.on('error', (error) => {
                    log(`Vapi error: ${error.message}`, 'error');
                    updateStatus('call-status', `Error: ${error.message}`, 'error');
                });

                // Start the call
                await vapi.start(CONFIG.assistantId);
                log('Call start command sent', 'info');

            } catch (error) {
                log(`Failed to start call: ${error.message}`, 'error');
                updateStatus('call-status', `Failed to start call: ${error.message}`, 'error');
            }
        }

        // Stop Vapi Call
        async function stopVapiCall() {
            log('Stopping Vapi call...', 'info');
            
            try {
                if (vapi && isCallActive) {
                    await vapi.stop();
                    log('Call stop command sent', 'info');
                } else {
                    log('No active call to stop', 'warning');
                }
            } catch (error) {
                log(`Failed to stop call: ${error.message}`, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            log('Page loaded, initializing...', 'info');
            displayConfig();
        });
    </script>
</body>
</html>
